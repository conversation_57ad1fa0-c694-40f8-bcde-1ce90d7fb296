package com.challanty.android.kp3.viewModel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.challanty.android.kp3.data.repository.Repository
import com.challanty.android.kp3.util.Constants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the Settings screen.
 */
@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val repository: Repository
) : ViewModel() {

    // User's in-progress changes (these persist across navigation)
    private val _pendingBoardRows = MutableStateFlow<Int?>(null)
    private val _pendingBoardCols = MutableStateFlow<Int?>(null)
    private val _pendingTileRows = MutableStateFlow<Int?>(null)
    private val _pendingTileCols = MutableStateFlow<Int?>(null)
    private val _pendingLockPercent = MutableStateFlow<Int?>(null)
    private val _pendingTilesRotatable = MutableStateFlow<Boolean?>(null)
    private val _pendingAnimate = MutableStateFlow<Boolean?>(null)
    private val _pendingAnimateRotation = MutableStateFlow<Boolean?>(null)
    private val _pendingAnimateSwap = MutableStateFlow<Boolean?>(null)

    // Exposed values that combine current and pending values
    // Note: combine() only makes a Flow, so we need to convert back to a StateFlow
    val settingsBoardRows: StateFlow<Int> = combine(
        repository.settingsStateFlow,
        _pendingBoardRows
    ) { settings, pending ->
        println("SettingsViewModel: settingsBoardRows = ${pending ?: settings.boardRows}")
        pending ?: settings.boardRows
    }.onEach { value: Int ->
        println("SettingsViewModel: settingsBoardRows produced: $value")
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = 0
    )

    val settingBoardCols: StateFlow<Int> = combine(
        repository.settingsStateFlow,
        _pendingBoardCols
    ) { settings, pending ->
        println("SettingsViewModel: settingBoardCols = ${pending ?: settings.boardCols}")
        pending ?: settings.boardCols
    }.onEach { value: Int ->
        println("SettingsViewModel: settingBoardCols produced: $value")
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = 0
    )

    val settingsTileRows: StateFlow<Int> = combine(
        repository.settingsStateFlow,
        _pendingTileRows
    ) { settings, pending ->
        pending ?: settings.tileRows
    }.onEach { value: Int ->
        println("SettingsViewModel: settingsTileRows produced: $value")
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = 0
    )

    val settingsTileCols: StateFlow<Int> = combine(
        repository.settingsStateFlow,
        _pendingTileCols
    ) { settings, pending ->
        pending ?: settings.tileCols
    }.onEach { value: Int ->
        println("SettingsViewModel: settingsTileCols produced: $value")
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = 0
    )

    val settingsLockPercent: StateFlow<Int> = combine(
        repository.settingsStateFlow,
        _pendingLockPercent
    ) { settings, pending ->
        pending ?: settings.lockPercent
    }.onEach { value: Int ->
        println("SettingsViewModel: settingsLockPercent produced: $value")
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = 0
    )

    val settingsTilesRotatable: StateFlow<Boolean> = combine(
        repository.settingsStateFlow,
        _pendingTilesRotatable
    ) { settings, pending ->
        pending ?: settings.tilesRotatable
    }.onEach { value: Boolean ->
        println("SettingsViewModel: settingsTilesRotatable produced: $value")
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )

    val settingsAnimate: StateFlow<Boolean> = combine(
        repository.settingsStateFlow,
        _pendingAnimate
    ) { settings, pending ->
        pending ?: settings.animate
    }.onEach { value: Boolean ->
        println("SettingsViewModel: settingsAnimate produced: $value")
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )

    val settingsAnimateRotation: StateFlow<Boolean> = combine(
        repository.settingsStateFlow,
        _pendingAnimateRotation
    ) { settings, pending ->
        pending ?: settings.animateRotation
    }.onEach { value: Boolean ->
        println("SettingsViewModel: settingsAnimateRotation produced: $value")
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )

    val settingsAnimateSwap: StateFlow<Boolean> = combine(
        repository.settingsStateFlow,
        _pendingAnimateSwap
    ) { settings, pending ->
        pending ?: settings.animateSwap
    }.onEach { value: Boolean ->
        println("SettingsViewModel: settingsAnimateSwap produced: $value")
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )

    // Whether tiles can be rotated based on tile dimensions
    val settingsCanRotateTiles: StateFlow<Boolean> = combine(
        settingsTileRows,
        settingsTileCols
    ) { rows, columns ->
        rows == columns // Tiles can only be rotated if they are square
    }.onEach { value: Boolean ->
        println("SettingsViewModel: settingsCanRotateTiles produced: $value")
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )

    // TODO why was it done this way? Why can't we just use settingsBoardRows?
    // Valid values for tile rows based on board rows
//    val settingsValidTileRowValues: StateFlow<List<Int>> = settingsBoardRows.stateIn(
//        scope = viewModelScope,
//        started = SharingStarted.WhileSubscribed(5000),
//        initialValue = Constants.DEFAULT_BOARD_ROWS
//    ).combine(settingsTileRows) { boardRowsValue, tileRowsValue ->
//        if (boardRowsValue % 2 == 1) {
//            // If matrix rows is odd, only allow even tile rows
//            Constants.LIST_OF_EVEN_DIMENSIONS
//        } else {
//            // If matrix rows is even, allow all tile row values
//            Constants.LIST_OF_ALL_DIMENSIONS
//        }
//    } as StateFlow<List<Int>>

    val settingsValidTileRowValues: StateFlow<List<Int>> = combine(
        settingsBoardRows,
        settingsTileRows
    ) { boardRowsValue, tileRowsValue ->
        if (boardRowsValue % 2 == 1) {
            // If matrix rows is odd, only allow even tile rows
            Constants.LIST_OF_EVEN_DIMENSIONS
        } else {
            // If matrix rows is even, allow all tile row values
            Constants.LIST_OF_ALL_DIMENSIONS
        }
    }.onEach { value: List<Int> ->
        println("SettingsViewModel: settingsValidTileRowValues produced: $value")
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = Constants.LIST_OF_ALL_DIMENSIONS
    )

    // TODO why was it done this way? Why can't we just use settingsBoardCols?
    // Valid values for tile columns based on matrix columns
//    val settingsValidTileColumnValues: StateFlow<List<Int>> = settingBoardCols.stateIn(
//        scope = viewModelScope,
//        started = SharingStarted.WhileSubscribed(5000),
//        initialValue = Constants.DEFAULT_BOARD_COLS
//    ).combine(settingsTileColumns) { matrixColumnsValue, tileColumnsValue ->
//        if (matrixColumnsValue % 2 == 1) {
//            // If matrix columns is odd, only allow even tile columns
//            Constants.LIST_OF_EVEN_DIMENSIONS
//        } else {
//            // If matrix columns is even, allow all values
//            Constants.LIST_OF_ALL_DIMENSIONS
//        }
//    }.stateIn(
//        scope = viewModelScope,
//        started = SharingStarted.WhileSubscribed(5000),
//        initialValue = Constants.LIST_OF_ALL_DIMENSIONS
//    )

    val settingsValidTileColValues: StateFlow<List<Int>> = combine(
        settingBoardCols,
        settingsTileCols
    ) { boardColsValue, tileColsValue ->
        if (boardColsValue % 2 == 1) {
            // If board columns is odd, only allow even tile columns
            Constants.LIST_OF_EVEN_DIMENSIONS
        } else {
            // If board columns is even, allow all values
            Constants.LIST_OF_ALL_DIMENSIONS
        }
    }.onEach { value: List<Int> ->
        println("SettingsViewModel: settingsValidTileColValues produced: $value")
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = Constants.LIST_OF_ALL_DIMENSIONS
    )

    // Check if board dimensions have pending changes
    // Note: There are no subscribers to this flow, so it must be Eagerly
    private val hasBoardPendingChanges: StateFlow<Boolean> = combine(
        repository.settingsStateFlow,
        _pendingBoardRows,
        _pendingBoardCols
    ) { settings, pendingBoardR, pendingBoardC ->
        println("SettingsViewModel: hasBoardPendingChanges() = ${pendingBoardR != null && pendingBoardR != settings.boardRows} || ${pendingBoardC != null && pendingBoardC != settings.boardCols}")
        (pendingBoardR != null && pendingBoardR != settings.boardRows) ||
                (pendingBoardC != null && pendingBoardC != settings.boardCols)
    }.onEach { value: Boolean ->
        println("SettingsViewModel: hasBoardPendingChanges produced: $value")
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = false
    )

    // Check if tile dimensions have pending changes
    // Note: There are no subscribers to this flow, so it must be Eagerly
    private val hasTilePendingChanges: StateFlow<Boolean> = combine(
        repository.settingsStateFlow,
        _pendingTileRows,
        _pendingTileCols,
        _pendingTilesRotatable
    ) { settings, pendingTileR, pendingTileC, pendingRotatable ->
        println("SettingsViewModel: hasTilePendingChanges() = ${pendingTileR != null && pendingTileR != settings.tileRows} || ${pendingTileC != null && pendingTileC != settings.tileCols} || ${pendingRotatable != null && pendingRotatable != settings.tilesRotatable}")
        (pendingTileR != null && pendingTileR != settings.tileRows) ||
                (pendingTileC != null && pendingTileC != settings.tileCols) ||
                (pendingRotatable != null && pendingRotatable != settings.tilesRotatable)
    }.onEach { value: Boolean ->
        println("SettingsViewModel: hasTilePendingChanges produced: $value")
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = false
    )

    // Check if animation settings have pending changes
    // Note: There are no subscribers to this flow, so it must be Eagerly
    private val hasAnimationPendingChanges: StateFlow<Boolean> = combine(
        repository.settingsStateFlow,
        _pendingAnimate,
        _pendingAnimateRotation,
        _pendingAnimateSwap
    ) { settings, pendingAnimate, pendingAnimateRotation, pendingAnimateSwap ->
        (pendingAnimate != null && pendingAnimate != settings.animate) ||
                (pendingAnimateRotation != null && pendingAnimateRotation != settings.animateRotation) ||
                (pendingAnimateSwap != null && pendingAnimateSwap != settings.animateSwap)
    }.onEach { value: Boolean ->
        println("SettingsViewModel: hasAnimationPendingChanges produced: $value")
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = false
    )

    // Flag to indicate if there are any pending changes
    // Note: There are no subscribers to this flow, so it must be Eagerly
    val hasPendingChanges: StateFlow<Boolean> = combine(
        hasBoardPendingChanges,
        hasTilePendingChanges,
        hasAnimationPendingChanges
    ) { hasMatrixChanges, hasTileChanges, hasAnimationChanges ->
        println("SettingsViewModel: hasPendingChanges() = $hasMatrixChanges || $hasTileChanges || $hasAnimationChanges")
        hasMatrixChanges || hasTileChanges || hasAnimationChanges
    }.onEach { value: Boolean ->
        println("SettingsViewModel: hasPendingChanges produced: $value")
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = false
    )

    /**
     * Update the pending number of rows in the game board.
     * This doesn't update the repository yet, just stores the pending change.
     * Also adjusts tile rows if needed to maintain even product constraint.
     */
    fun setPendingBoardRows(rows: Int) {
        println("SettingsViewModel: setPendingBoardRows($rows)")
        _pendingBoardRows.value = rows

        // If matrix rows is now odd, ensure tile rows is even
        if (rows % 2 == 1) {
            val currentTileRowsValue: Int =
                _pendingTileRows.value ?: repository.settingsStateFlow.value.tileRows

            if (currentTileRowsValue % 2 == 1) {
                // Current tile rows is odd, need to adjust
                if (currentTileRowsValue > 1) {
                    // Decrease by 1 to make it even
                    _pendingTileRows.value = currentTileRowsValue - 1
                } else {
                    // If it's 1, set to 2
                    _pendingTileRows.value = 2
                }
            }
        }
    }

    /**
     * Update the pending number of columns in the game board.
     * This doesn't update the repository yet, just stores the pending change.
     * Also adjusts tile columns if needed to maintain even product constraint.
     */
    fun setPendingBoardCols(cols: Int) {
        println("SettingsViewModel: setPendingBoardCols($cols)")
        _pendingBoardCols.value = cols

        // If matrix columns is now odd, ensure tile columns is even
        if (cols % 2 == 1) {
            val currentTileColsValue =
                _pendingTileCols.value ?: repository.settingsStateFlow.value.tileCols

            if (currentTileColsValue % 2 == 1) {
                // Current tile columns is odd, need to adjust
                if (currentTileColsValue > 1) {
                    // Decrease by 1 to make it even
                    _pendingTileCols.value = currentTileColsValue - 1
                } else {
                    // If it's 1, set to 2
                    _pendingTileCols.value = 2
                }
            }
        }
    }

    /**
     * Update the pending number of rows in each tile's picture elements.
     * This doesn't update the repository yet, just stores the pending change.
     */
    fun setPendingTileRows(rows: Int) {
        val boardRowsValue = _pendingBoardRows.value ?: repository.settingsStateFlow.value.boardRows

        // If board rows is odd, ensure tile rows is even
        if (boardRowsValue % 2 == 1 && rows % 2 == 1) {
            // Can't set an odd value for tile rows when board rows is odd
            return
        }

        println("SettingsViewModel: setPendingTileRows($rows)")
        _pendingTileRows.value = rows

        // Check if we need to update the rotatable setting
        updateRotatableSetting()
    }

    /**
     * Update the pending number of columns in each tile's picture elements.
     * This doesn't update the repository yet, just stores the pending change.
     */
    fun setPendingTileCols(cols: Int) {
        val boardColsValue = _pendingBoardCols.value ?: repository.settingsStateFlow.value.boardCols

        // If board columns is odd, ensure tile columns is even
        if (boardColsValue % 2 == 1 && cols % 2 == 1) {
            // Can't set an odd value for tile columns when board columns is odd
            return
        }

        println("SettingsViewModel: setPendingTileCols($cols)")
        _pendingTileCols.value = cols

        // Check if we need to update the rotatable setting
        updateRotatableSetting()
    }

    fun setPendingLockPercent(cols: Int) {
        val lockPercentValue = _pendingBoardCols.value ?: repository.settingsStateFlow.value.boardCols

        // If board columns is odd, ensure tile columns is even
        if (lockPercentValue % 2 == 1 && cols % 2 == 1) {
            // Can't set an odd value for tile columns when board columns is odd
            return
        }

        println("SettingsViewModel: setPendingTileCols($cols)")
        _pendingTileCols.value = cols

        // Check if we need to update the rotatable setting
        updateRotatableSetting()
    }

    /**
     * Update the pending setting for whether tiles can be rotated.
     * This doesn't update the repository yet, just stores the pending change.
     */
    fun setPendingTilesRotatable(rotatable: Boolean) {
        // Only allow setting to true if tiles are square
        val settings = repository.settingsStateFlow.value
        val tileRowsValue = _pendingTileRows.value ?: settings.tileRows
        val tileColsValue = _pendingTileCols.value ?: settings.tileCols

        if (rotatable && tileRowsValue != tileColsValue) {
            // Can't enable rotation for non-square tiles
            return
        }

        println("SettingsViewModel: setPendingTilesRotatable($rotatable)")
        _pendingTilesRotatable.value = rotatable
    }

    /**
     * Update the rotatable setting based on tile dimensions.
     * If tiles are not square, rotation must be disabled.
     */
    private fun updateRotatableSetting() {
        val settings = repository.settingsStateFlow.value
        val tileRowsValue = _pendingTileRows.value ?: settings.tileRows
        val tileColsValue = _pendingTileCols.value ?: settings.tileCols

        // If tiles are not square, force rotatable to false
        if (tileRowsValue != tileColsValue) {
            println("SettingsViewModel: updateRotatableSetting() - tiles are not square, forcing rotatable to false")
            _pendingTilesRotatable.value = false
        }
    }

    /**
     * Update the pending setting for whether to animate game actions.
     * This doesn't update the repository yet, just stores the pending change.
     */
    fun setPendingAnimate(animate: Boolean) {
        println("SettingsViewModel: setPendingAnimate($animate)")
        _pendingAnimate.value = animate
    }

    /**
     * Update the pending setting for whether to animate tile rotations.
     * This doesn't update the repository yet, just stores the pending change.
     */
    fun setPendingAnimateRotation(animateRotation: Boolean) {
        println("SettingsViewModel: setPendingAnimateRotation($animateRotation)")
        _pendingAnimateRotation.value = animateRotation
    }

    /**
     * Update the pending setting for whether to animate tile swaps.
     * This doesn't update the repository yet, just stores the pending change.
     */
    fun setPendingAnimateSwap(animateSwap: Boolean) {
        println("SettingsViewModel: setPendingAnimateSwap($animateSwap)")
        _pendingAnimateSwap.value = animateSwap
    }

    fun applyPendingChanges() {
        // If there are no pending changes, do nothing
        if (!hasPendingChanges.value) return

        println("SettingsViewModel: applyChanges()")
        val pendingBoardRows = _pendingBoardRows.value
        val pendingBoardCols = _pendingBoardCols.value
        val pendingTileRows = _pendingTileRows.value
        val pendingTileCols = _pendingTileCols.value
        val pendingLockPercent = _pendingLockPercent.value
        val pendingTilesRotatable = _pendingTilesRotatable.value
        val pendingAnimate = _pendingAnimate.value
        val pendingAnimateRotation = _pendingAnimateRotation.value
        val pendingAnimateSwap = _pendingAnimateSwap.value

        val settings = repository.settingsStateFlow.value

        // TODO is there an easier way to do this?
        // Note: The whole Settings datastore message gets written, so we
        // need to supply values for all fields.
        repository.pendingSetVersion(settings.version)
        repository.pendingSetBoardRows(pendingBoardRows ?: settings.boardRows)
        repository.pendingSetBoardCols(pendingBoardCols ?: settings.boardCols)
        repository.pendingSetTileRows(pendingTileRows ?: settings.tileRows)
        repository.pendingSetTileCols(pendingTileCols ?: settings.tileCols)
        repository.pendingSetLockPercent(pendingLockPercent ?: settings.lockPercent)
        repository.pendingSetTilesRotatable(pendingTilesRotatable ?: settings.tilesRotatable)
        repository.pendingSetAnimate(pendingAnimate ?: settings.animate)
        repository.pendingSetAnimateRotation(pendingAnimateRotation ?: settings.animateRotation)
        repository.pendingSetAnimateSwap(pendingAnimateSwap ?: settings.animateSwap)

        viewModelScope.launch {
            repository.onPendingFinalized()
        }

        // Clear pending changes after applying them
        _pendingBoardRows.value = null
        _pendingBoardCols.value = null
        _pendingTileRows.value = null
        _pendingTileCols.value = null
        _pendingLockPercent.value = null
        _pendingTilesRotatable.value = null
        _pendingAnimate.value = null
        _pendingAnimateRotation.value = null
        _pendingAnimateSwap.value = null
    }

    /**
     * Called when the ViewModel is being cleared (e.g., when the user navigates away
     * or when the app is shut down).
     */
    override fun onCleared() {
        super.onCleared()
        // Apply any pending changes before the ViewModel is cleared
        if (hasPendingChanges.value) {
            applyPendingChanges()
        }
    }
}
