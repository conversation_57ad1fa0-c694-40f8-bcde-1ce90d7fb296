package com.challanty.android.kp3.viewModel

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.unit.IntOffset

/**
 * ViewModel for managing the state of an individual tile.
 * This allows for more granular state updates without recomposing the entire board.
 */
class TileModel(
    val id: Int, // Unique identifier for the tile
    val bitmap: ImageBitmap, // Static bitmap for the tile
    var boardPosition: IntOffset = IntOffset.Zero, // Current row/column position on the board
    initIntOffset: IntOffset = IntOffset.Zero, // Animatable offset from its layout position
    initIntOffsetDuration: Int = 1000, // Duration for intOffset animation
    initQuarterTurnCnt: Int = 0, // Number of quarter turns to rotate the tile during game layout
    initRotationDuration: Int = 1000, // Duration for rotation animation
    initIsLocked: Boolean = false, // State for Lock visibility/animation
    initIsSelected: Boolean = false, // State for Selected border
) {

    // Mutable properties that can trigger recomposition when changed
    var intOffset by mutableStateOf(initIntOffset)
        private set
    var intOffsetDuration by mutableStateOf(initIntOffsetDuration)
        private set
    var quarterTurnCnt by mutableStateOf(initQuarterTurnCnt)
        private set
    var rotationDuration by mutableStateOf(initRotationDuration)
        private set
    var isLocked by mutableStateOf(initIsLocked)
        private set
    var isSelected by mutableStateOf(initIsSelected)
        private set
    
    /**
     * Updates the tile's placement properties.
     */
    fun update(
        boardPosition: IntOffset,
        offsetX: Int,
        offsetY: Int,
        offsetDuration: Int,
        quarterTurnCnt: Int,
        rotationDuration: Int
    ) {
        this.boardPosition = boardPosition
        intOffset = IntOffset(offsetX, offsetY)
        intOffsetDuration = offsetDuration
        this.quarterTurnCnt = quarterTurnCnt
        this.rotationDuration = rotationDuration
    }
    
    /**
     * Toggles the selection state of the tile.
     */
    fun toggleSelection() {
        isSelected = !isSelected
    }
    
    /**
     * Toggles the locked state of the tile.
     */
    fun toggleLock() {
        isLocked = !isLocked
    }
}
