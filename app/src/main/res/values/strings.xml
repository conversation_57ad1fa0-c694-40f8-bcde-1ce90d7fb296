<resources>
    <!-- App name -->
    <string name="app_name">KnotPuzzled</string>

    <!-- Screen titles -->
    <string name="screen_title_loading">KnotPuzzled</string>
    <string name="screen_title_game">KnotPuzzled</string>
    <string name="screen_title_settings">KnotPuzzled Settings</string>
    <string name="screen_title_help">KnotPuzzled Help</string>
    <string name="screen_title_about">KnotPuzzled About</string>

    <!-- Navigation bar items -->
    <string name="nav_game">Game</string>
    <string name="nav_settings">Settings</string>
    <string name="nav_help">Help</string>
    <string name="nav_about">About</string>

    <!-- Settings screen -->
    <string name="settings_title">Game Settings</string>
    <string name="settings_board_dimensions">Board Dimensions</string>
    <string name="settings_board_rows">Board Rows: %1$d</string>
    <string name="settings_board_cols">Board Columns: %1$d</string>
    <string name="settings_current_matrix_size">Board Size: %1$d × %2$d</string>
    <string name="settings_tile_dimensions">Tile Dimensions</string>
    <string name="settings_tile_rows">Tile Rows: %1$d</string>
    <string name="settings_tile_columns">Tile Columns: %1$d</string>
    <string name="settings_current_tile_size">Tile Size: %1$d × %2$d</string>
    <string name="settings_tile_rows_constraint">When board rows is odd, tile rows must be even (2, 4, 6)</string>
    <string name="settings_tile_columns_constraint">When board columns is odd, tile columns must be even (2, 4, 6)</string>
    <string name="settings_visual_dimensions">Adjust Dimensions</string>
    <string name="settings_board_dimensions_display">Board: %1$d × %2$d</string>
    <string name="settings_tile_dimensions_display">Tile: %1$d × %2$d</string>
    <string name="settings_constraint_explanation">When board rows (or columns) is odd, tile rows (or columns) must be even.</string>
    <string name="settings_rotatable_tiles">Allow Rotating Tiles</string>
    <string name="settings_rotatable_constraint">Tiles can only be rotated when they have the same number of rows and columns (square tiles).</string>
    <string name="settings_animation">Animation Settings</string>
    <string name="settings_animate">Enable Animations</string>
    <string name="settings_animate_rotation">Animate Tile Rotations</string>
    <string name="settings_animate_swap">Animate Tile Swaps</string>
    <string name="settings_apply_changes">Apply Changes</string>
    <string name="settings_discard_changes">Discard Changes</string>
    <string name="settings_description">The board dimensions determine the number of tiles in the puzzle. The tile dimensions determine the tile detail level.</string>

    <!-- Game screen -->
    <string name="game_placeholder">Game Screen\nWork Area</string>
    <string name="remaining_tile_locks">Remaining Tile Locks</string>
    <string name="game_new_game">New Game</string>
    <string name="game_loading">Loading game…</string>

    <!-- Help screen -->
    <string name="help_placeholder">Help Screen\nWork Area</string>

    <!-- About screen -->
    <string name="about_placeholder">About Screen\nWork Area</string>

    <!-- Loading screen -->
    <string name="loading_placeholder">Loading…</string>
</resources>